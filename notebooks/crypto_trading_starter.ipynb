{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Crypto Trading with FinRL - Getting Started\n", "\n", "This notebook provides a step-by-step guide to get started with cryptocurrency trading using FinRL.\n", "\n", "## Table of Contents\n", "1. [Setup and Imports](#setup)\n", "2. [Data Download and Preprocessing](#data)\n", "3. [Environment Creation](#environment)\n", "4. [Model Training](#training)\n", "5. [Backtesting](#backtesting)\n", "6. [Results Analysis](#analysis)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports {#setup}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import sys\n", "import os\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add project root to path\n", "sys.path.append('..')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "\n", "# FinRL imports\n", "from finrl.meta.preprocessor.yahoodownloader import YahooDownloader\n", "from finrl.meta.preprocessor.preprocessors import FeatureEngineer, data_split\n", "from finrl.meta.env_cryptocurrency_trading.env_cryptocurrency import CryptocurrencyEnv\n", "\n", "# Stable Baselines3 imports\n", "from stable_baselines3 import PPO, A2C\n", "from stable_baselines3.common.vec_env import DummyVecEnv\n", "\n", "# Project imports\n", "from config.config import CRYPTO_CONFIG, TRADING_CONFIG, MODEL_CONFIG\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Download and Preprocessing {#data}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download cryptocurrency data\n", "print(\"Downloading cryptocurrency data...\")\n", "print(f\"Symbols: {CRYPTO_CONFIG['symbols']}\")\n", "print(f\"Date range: {CRYPTO_CONFIG['start_date']} to {CRYPTO_CONFIG['end_date']}\")\n", "\n", "# Use FinRL's YahooDownloader\n", "df_raw = YahooDownloader(\n", "    start_date=CRYPTO_CONFIG[\"start_date\"],\n", "    end_date=CRYPTO_CONFIG[\"end_date\"],\n", "    ticker_list=CRYPTO_CONFIG[\"symbols\"]\n", ").fetch_data()\n", "\n", "print(f\"✅ Downloaded {len(df_raw)} rows of data\")\n", "print(f\"Data shape: {df_raw.shape}\")\n", "print(f\"Columns: {list(df_raw.columns)}\")\n", "\n", "# Display first few rows\n", "df_raw.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add technical indicators\n", "print(\"Adding technical indicators...\")\n", "print(f\"Indicators: {CRYPTO_CONFIG['technical_indicators']}\")\n", "\n", "fe = FeatureEngineer(\n", "    use_technical_indicator=True,\n", "    tech_indicator_list=CRYPTO_CONFIG[\"technical_indicators\"],\n", "    use_vix=False,\n", "    use_turbulence=True,\n", "    user_defined_feature=False\n", ")\n", "\n", "df_processed = fe.preprocess_data(df_raw)\n", "\n", "print(f\"✅ Processed data shape: {df_processed.shape}\")\n", "print(f\"New columns: {[col for col in df_processed.columns if col not in df_raw.columns]}\")\n", "\n", "# Display processed data\n", "df_processed.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data visualization\n", "plt.figure(figsize=(15, 10))\n", "\n", "# Plot price data for each cryptocurrency\n", "for i, symbol in enumerate(CRYPTO_CONFIG['symbols']):\n", "    plt.subplot(2, 3, i+1)\n", "    symbol_data = df_processed[df_processed['tic'] == symbol]\n", "    plt.plot(pd.to_datetime(symbol_data['date']), symbol_data['close'])\n", "    plt.title(f'{symbol} Price')\n", "    plt.xlabel('Date')\n", "    plt.ylabel('Price (USD)')\n", "    plt.xticks(rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Summary statistics\n", "print(\"\\nSummary Statistics:\")\n", "df_processed.groupby('tic')['close'].agg(['mean', 'std', 'min', 'max']).round(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Environment Creation {#environment}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data into training and testing sets\n", "train_start = \"2020-01-01\"\n", "train_end = \"2022-01-01\"\n", "test_start = \"2022-01-01\"\n", "test_end = \"2024-01-01\"\n", "\n", "train_data = data_split(df_processed, start=train_start, end=train_end)\n", "test_data = data_split(df_processed, start=test_start, end=test_end)\n", "\n", "print(f\"Training data: {len(train_data)} rows ({train_start} to {train_end})\")\n", "print(f\"Testing data: {len(test_data)} rows ({test_start} to {test_end})\")\n", "\n", "# Create trading environment\n", "env_kwargs = {\n", "    \"initial_amount\": TRADING_CONFIG[\"initial_amount\"],\n", "    \"transaction_cost_pct\": TRADING_CONFIG[\"transaction_cost_pct\"],\n", "    \"mode\": \"train\",\n", "    \"day_trading\": TRADING_CONFIG[\"day_trading\"]\n", "}\n", "\n", "# Create environment for training\n", "train_env = CryptocurrencyEnv(df=train_data, **env_kwargs)\n", "train_env = DummyVecEnv([lambda: train_env])\n", "\n", "print(f\"✅ Training environment created\")\n", "print(f\"Action space: {train_env.action_space}\")\n", "print(f\"Observation space: {train_env.observation_space}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Training {#training}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train PPO model (recommended for beginners)\n", "print(\"Training PPO model...\")\n", "\n", "model_ppo = PPO(\n", "    \"MlpPolicy\", \n", "    train_env, \n", "    verbose=1,\n", "    learning_rate=0.0003,\n", "    n_steps=2048,\n", "    batch_size=64,\n", "    n_epochs=10\n", ")\n", "\n", "# Train the model\n", "model_ppo.learn(\n", "    total_timesteps=50000,  # Reduced for demo purposes\n", "    log_interval=100\n", ")\n", "\n", "print(\"✅ PPO model training completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train A2C model for comparison\n", "print(\"Training A2C model...\")\n", "\n", "model_a2c = A2C(\n", "    \"MlpPolicy\", \n", "    train_env, \n", "    verbose=1,\n", "    learning_rate=0.0007,\n", "    n_steps=5\n", ")\n", "\n", "# Train the model\n", "model_a2c.learn(\n", "    total_timesteps=50000,  # Reduced for demo purposes\n", "    log_interval=100\n", ")\n", "\n", "print(\"✅ A2C model training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Backtesting {#backtesting}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def backtest_model(model, test_data, model_name):\n", "    \"\"\"Backtest a trained model\"\"\"\n", "    print(f\"Backtesting {model_name} model...\")\n", "    \n", "    # Create test environment\n", "    test_env_kwargs = env_kwargs.copy()\n", "    test_env_kwargs[\"mode\"] = \"trade\"\n", "    \n", "    test_env = CryptocurrencyEnv(df=test_data, **test_env_kwargs)\n", "    \n", "    # Run backtest\n", "    obs = test_env.reset()\n", "    done = False\n", "    \n", "    while not done:\n", "        action, _ = model.predict(obs)\n", "        obs, reward, done, info = test_env.step(action)\n", "    \n", "    # Get results\n", "    results = test_env.save_asset_memory()\n", "    \n", "    return results\n", "\n", "# Backtest both models\n", "results_ppo = backtest_model(model_ppo, test_data, \"PPO\")\n", "results_a2c = backtest_model(model_a2c, test_data, \"A2C\")\n", "\n", "print(\"✅ Backtesting completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Results Analysis {#analysis}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate performance metrics\n", "def calculate_metrics(results, model_name):\n", "    \"\"\"Calculate trading performance metrics\"\"\"\n", "    results['date'] = pd.to_datetime(results['date'])\n", "    results['daily_return'] = results['total_assets'].pct_change()\n", "    \n", "    # Calculate metrics\n", "    total_return = (results['total_assets'].iloc[-1] / results['total_assets'].iloc[0] - 1) * 100\n", "    annual_return = ((results['total_assets'].iloc[-1] / results['total_assets'].iloc[0]) ** (252/len(results)) - 1) * 100\n", "    volatility = results['daily_return'].std() * np.sqrt(252) * 100\n", "    sharpe_ratio = annual_return / volatility if volatility > 0 else 0\n", "    max_drawdown = ((results['total_assets'] / results['total_assets'].cummax()) - 1).min() * 100\n", "    \n", "    metrics = {\n", "        'Model': model_name,\n", "        'Total Return (%)': round(total_return, 2),\n", "        'Annual Return (%)': round(annual_return, 2),\n", "        'Volatility (%)': round(volatility, 2),\n", "        'Sharpe Ratio': round(sharpe_ratio, 2),\n", "        'Max Drawdown (%)': round(max_drawdown, 2),\n", "        'Final Value ($)': round(results['total_assets'].iloc[-1], 2)\n", "    }\n", "    \n", "    return metrics\n", "\n", "# Calculate metrics for both models\n", "metrics_ppo = calculate_metrics(results_ppo, \"PPO\")\n", "metrics_a2c = calculate_metrics(results_a2c, \"A2C\")\n", "\n", "# Create comparison table\n", "comparison_df = pd.DataFrame([metrics_ppo, metrics_a2c])\n", "print(\"Performance Comparison:\")\n", "print(comparison_df.to_string(index=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot portfolio performance\n", "plt.figure(figsize=(15, 8))\n", "\n", "# Plot portfolio values\n", "plt.subplot(2, 1, 1)\n", "plt.plot(pd.to_datetime(results_ppo['date']), results_ppo['total_assets'], label='PPO', linewidth=2)\n", "plt.plot(pd.to_datetime(results_a2c['date']), results_a2c['total_assets'], label='A2C', linewidth=2)\n", "plt.axhline(y=TRADING_CONFIG['initial_amount'], color='black', linestyle='--', label='Initial Capital')\n", "plt.title('Portfolio Value Over Time')\n", "plt.xlabel('Date')\n", "plt.ylabel('Portfolio Value ($)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Plot daily returns\n", "plt.subplot(2, 1, 2)\n", "plt.plot(pd.to_datetime(results_ppo['date']), results_ppo['total_assets'].pct_change(), label='PPO Daily Returns', alpha=0.7)\n", "plt.plot(pd.to_datetime(results_a2c['date']), results_a2c['total_assets'].pct_change(), label='A2C Daily Returns', alpha=0.7)\n", "plt.title('Daily Returns')\n", "plt.xlabel('Date')\n", "plt.ylabel('Daily Return')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save results\n", "print(\"Saving results...\")\n", "\n", "# Create results directory if it doesn't exist\n", "os.makedirs('../results/backtests', exist_ok=True)\n", "\n", "# Save individual results\n", "results_ppo.to_csv('../results/backtests/ppo_backtest_results.csv', index=False)\n", "results_a2c.to_csv('../results/backtests/a2c_backtest_results.csv', index=False)\n", "\n", "# Save comparison\n", "comparison_df.to_csv('../results/backtests/model_comparison.csv', index=False)\n", "\n", "print(\"✅ Results saved to ../results/backtests/\")\n", "print(\"\\n🎉 Crypto trading analysis completed!\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Experiment with different hyperparameters\")\n", "print(\"2. Try other algorithms (SAC, DDPG)\")\n", "print(\"3. Add more cryptocurrencies\")\n", "print(\"4. Implement custom technical indicators\")\n", "print(\"5. Set up paper trading for live testing\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}