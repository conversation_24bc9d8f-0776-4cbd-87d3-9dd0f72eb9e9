#!/usr/bin/env python3
"""
Quick Start Script for Crypto Trading with FinRL
This script helps you get started quickly with the crypto trading project
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ Python 3.6 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("\n🔧 Manual installation required:")
        print("pip install finrl stable-baselines3 yfinance ccxt ta jupyter")
        return False

def check_installation():
    """Check if key packages are installed"""
    print("\n🔍 Checking installation...")
    packages = ["finrl", "stable_baselines3", "yfinance", "ccxt", "ta", "pandas", "numpy"]
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - not installed")
            return False
    
    print("✅ All packages installed correctly!")
    return True

def setup_environment():
    """Setup environment variables"""
    print("\n🔧 Setting up environment...")
    
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file already exists")
        print("📝 Please update it with your API keys:")
        print("   - BINANCE_API_KEY")
        print("   - BINANCE_SECRET_KEY") 
        print("   - ALPACA_API_KEY (for paper trading)")
        print("   - ALPACA_SECRET_KEY")
    else:
        print("❌ .env file not found. Run setup_crypto_trading.py first")
        return False
    
    return True

def run_data_download():
    """Download sample data"""
    print("\n📊 Downloading sample data...")
    try:
        result = subprocess.run([sys.executable, "src/data/download_data.py"], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ Data downloaded successfully!")
            return True
        else:
            print(f"❌ Error downloading data: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Data download timed out. You can run it manually later.")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_next_steps():
    """Show next steps to the user"""
    print("\n" + "="*60)
    print("🎉 Setup completed! Here's what you can do next:")
    print("="*60)
    
    print("\n📚 1. Learn with Jupyter Notebook:")
    print("   jupyter notebook notebooks/crypto_trading_starter.ipynb")
    
    print("\n🤖 2. Train your first model:")
    print("   python src/models/train.py")
    
    print("\n📊 3. Download fresh data:")
    print("   python src/data/download_data.py")
    
    print("\n🔧 4. Customize your strategy:")
    print("   - Edit config/config.py for trading parameters")
    print("   - Modify cryptocurrency list")
    print("   - Adjust technical indicators")
    
    print("\n📖 5. Learn more:")
    print("   - FinRL Documentation: https://finrl.readthedocs.io/")
    print("   - FinRL Tutorials: https://github.com/AI4Finance-Foundation/FinRL-Tutorials")
    print("   - Stable Baselines3: https://stable-baselines3.readthedocs.io/")
    
    print("\n⚠️  Important Notes:")
    print("   - Start with paper trading before using real money")
    print("   - Cryptocurrency trading involves significant risk")
    print("   - Always backtest strategies thoroughly")
    print("   - Consider consulting financial advisors")

def main():
    """Main function"""
    print("🚀 Crypto Trading with FinRL - Quick Start")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Check if project is set up
    if not Path("config/config.py").exists():
        print("\n❌ Project not set up. Running setup first...")
        try:
            subprocess.check_call([sys.executable, "setup_crypto_trading.py"])
        except subprocess.CalledProcessError:
            print("❌ Setup failed. Please run setup_crypto_trading.py manually")
            return
    
    # Install dependencies
    if not install_dependencies():
        print("\n⚠️  Continue with manual installation")
    
    # Check installation
    if not check_installation():
        print("\n❌ Some packages are missing. Please install them manually.")
        return
    
    # Setup environment
    setup_environment()
    
    # Download data (optional)
    print("\n❓ Would you like to download sample data now? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes']:
            run_data_download()
        else:
            print("⏭️  Skipping data download. You can run it later.")
    except KeyboardInterrupt:
        print("\n⏭️  Skipping data download.")
    
    # Show next steps
    show_next_steps()

if __name__ == "__main__":
    main()
