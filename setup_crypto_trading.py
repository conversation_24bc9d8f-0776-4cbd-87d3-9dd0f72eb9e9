#!/usr/bin/env python3
"""
Crypto Trading Project Setup Script using FinRL
This script sets up the complete environment for cryptocurrency trading using FinRL
"""

import os
import subprocess
import sys
from pathlib import Path

def create_project_structure():
    """Create the project directory structure"""
    directories = [
        "data/raw",
        "data/processed", 
        "data/indicators",
        "models/trained",
        "models/checkpoints",
        "results/backtests",
        "results/performance",
        "logs",
        "config",
        "notebooks",
        "src/data",
        "src/models",
        "src/trading",
        "src/utils",
        "tests"
    ]
    
    print("Creating project directory structure...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created: {directory}")

def create_requirements_file():
    """Create requirements.txt with necessary dependencies"""
    requirements = """
# FinRL and core dependencies
finrl>=0.3.6
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Deep Learning frameworks
torch>=1.10.0
stable-baselines3>=1.6.0
gym>=0.21.0

# Data sources and APIs
yfinance>=0.1.70
ccxt>=1.95.0
python-binance>=1.0.15

# Technical indicators
ta>=0.10.0
TA-Lib>=0.4.24

# Utilities
jupyter>=1.0.0
plotly>=5.0.0
dash>=2.0.0
python-dotenv>=0.19.0
requests>=2.27.0

# Development tools
pytest>=7.0.0
black>=22.0.0
flake8>=4.0.0
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements.strip())
    print("✓ Created requirements.txt")

def create_config_files():
    """Create configuration files"""
    
    # Main config file
    config_content = """
import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
RESULTS_DIR = PROJECT_ROOT / "results"
LOGS_DIR = PROJECT_ROOT / "logs"

# Trading configuration
TRADING_CONFIG = {
    "initial_amount": 100000,  # Starting capital in USD
    "transaction_cost_pct": 0.001,  # 0.1% transaction cost
    "max_stock": 1,  # Maximum position size
    "hmax": 100,  # Maximum shares to trade
    "print_verbosity": 2,
    "day_trading": True,
    "cache_indicator_data": True,
}

# Cryptocurrency configuration
CRYPTO_CONFIG = {
    "symbols": ["BTC-USD", "ETH-USD", "ADA-USD", "DOT-USD", "LINK-USD"],
    "start_date": "2020-01-01",
    "end_date": "2024-01-01",
    "time_interval": "1d",  # 1 day intervals
    "technical_indicators": [
        "macd", "boll_ub", "boll_lb", "rsi_30", "cci_30", 
        "dx_30", "close_30_sma", "close_60_sma"
    ]
}

# Model configuration
MODEL_CONFIG = {
    "algorithms": ["ppo", "a2c", "ddpg", "sac"],
    "total_timesteps": 100000,
    "eval_freq": 5000,
    "n_eval_episodes": 5,
    "log_interval": 100,
}

# Environment variables
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY", "")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY", "")
ALPACA_API_KEY = os.getenv("ALPACA_API_KEY", "")
ALPACA_SECRET_KEY = os.getenv("ALPACA_SECRET_KEY", "")
"""
    
    with open("config/config.py", "w") as f:
        f.write(config_content)
    print("✓ Created config/config.py")
    
    # Environment file
    env_content = """
# API Keys (Replace with your actual keys)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here

# Database (if using)
DATABASE_URL=sqlite:///crypto_trading.db

# Logging
LOG_LEVEL=INFO
"""
    
    with open(".env", "w") as f:
        f.write(env_content)
    print("✓ Created .env file")

def create_main_scripts():
    """Create main trading scripts"""
    
    # Data downloader script
    data_script = '''
"""
Cryptocurrency Data Downloader
Downloads and preprocesses cryptocurrency data for FinRL
"""

import pandas as pd
import yfinance as yf
from finrl.meta.preprocessor.yahoodownloader import YahooDownloader
from finrl.meta.preprocessor.preprocessors import FeatureEngineer, data_split
from config.config import CRYPTO_CONFIG, DATA_DIR
import os

def download_crypto_data():
    """Download cryptocurrency data"""
    print("Downloading cryptocurrency data...")
    
    # Download data using FinRL's YahooDownloader
    df = YahooDownloader(
        start_date=CRYPTO_CONFIG["start_date"],
        end_date=CRYPTO_CONFIG["end_date"],
        ticker_list=CRYPTO_CONFIG["symbols"]
    ).fetch_data()
    
    # Save raw data
    raw_data_path = DATA_DIR / "raw" / "crypto_raw_data.csv"
    df.to_csv(raw_data_path, index=False)
    print(f"✓ Raw data saved to {raw_data_path}")
    
    return df

def preprocess_data(df):
    """Add technical indicators and preprocess data"""
    print("Adding technical indicators...")
    
    # Feature engineering
    fe = FeatureEngineer(
        use_technical_indicator=True,
        tech_indicator_list=CRYPTO_CONFIG["technical_indicators"],
        use_vix=False,
        use_turbulence=True,
        user_defined_feature=False
    )
    
    processed_df = fe.preprocess_data(df)
    
    # Save processed data
    processed_data_path = DATA_DIR / "processed" / "crypto_processed_data.csv"
    processed_df.to_csv(processed_data_path, index=False)
    print(f"✓ Processed data saved to {processed_data_path}")
    
    return processed_df

if __name__ == "__main__":
    # Create data directories
    os.makedirs(DATA_DIR / "raw", exist_ok=True)
    os.makedirs(DATA_DIR / "processed", exist_ok=True)
    
    # Download and process data
    raw_data = download_crypto_data()
    processed_data = preprocess_data(raw_data)
    
    print("Data download and preprocessing completed!")
    print(f"Data shape: {processed_data.shape}")
    print(f"Date range: {processed_data['date'].min()} to {processed_data['date'].max()}")
'''
    
    with open("src/data/download_data.py", "w") as f:
        f.write(data_script)
    print("✓ Created src/data/download_data.py")

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("You may need to install some dependencies manually")

def create_trading_environment():
    """Create the trading environment script"""
    env_script = '''
"""
Cryptocurrency Trading Environment using FinRL
"""

import numpy as np
import pandas as pd
from finrl.meta.env_cryptocurrency_trading.env_cryptocurrency import CryptocurrencyEnv
from finrl.meta.preprocessor.preprocessors import data_split
from config.config import TRADING_CONFIG, CRYPTO_CONFIG, DATA_DIR
import gym

class CryptoTradingEnv:
    def __init__(self, df, mode="train"):
        self.df = df
        self.mode = mode
        self.env = None

    def create_env(self):
        """Create the trading environment"""
        env = CryptocurrencyEnv(
            df=self.df,
            initial_amount=TRADING_CONFIG["initial_amount"],
            transaction_cost_pct=TRADING_CONFIG["transaction_cost_pct"],
            mode=self.mode,
            day_trading=TRADING_CONFIG["day_trading"]
        )
        return env

    def get_sb_env(self):
        """Get environment compatible with stable-baselines3"""
        env = self.create_env()
        return env

def prepare_training_data():
    """Prepare data for training"""
    # Load processed data
    data_path = DATA_DIR / "processed" / "crypto_processed_data.csv"
    df = pd.read_csv(data_path)

    # Split data
    train_data = data_split(df, start="2020-01-01", end="2022-01-01")
    trade_data = data_split(df, start="2022-01-01", end="2024-01-01")

    return train_data, trade_data

if __name__ == "__main__":
    train_data, trade_data = prepare_training_data()

    # Create training environment
    train_env = CryptoTradingEnv(train_data, mode="train")
    env = train_env.get_sb_env()

    print(f"Environment created successfully!")
    print(f"Action space: {env.action_space}")
    print(f"Observation space: {env.observation_space}")
'''

    with open("src/trading/environment.py", "w") as f:
        f.write(env_script)
    print("✓ Created src/trading/environment.py")

def create_training_script():
    """Create the model training script"""
    training_script = '''
"""
Model Training Script for Crypto Trading
"""

import os
import pandas as pd
from stable_baselines3 import PPO, A2C, SAC, DDPG
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import EvalCallback
from finrl.meta.preprocessor.preprocessors import data_split
from src.trading.environment import CryptoTradingEnv, prepare_training_data
from config.config import MODEL_CONFIG, MODELS_DIR, RESULTS_DIR
import warnings
warnings.filterwarnings("ignore")

class CryptoTrader:
    def __init__(self):
        self.models = {}
        self.train_data = None
        self.trade_data = None

    def prepare_data(self):
        """Prepare training and testing data"""
        self.train_data, self.trade_data = prepare_training_data()
        print(f"Training data shape: {self.train_data.shape}")
        print(f"Trading data shape: {self.trade_data.shape}")

    def create_env(self, data, mode="train"):
        """Create trading environment"""
        env = CryptoTradingEnv(data, mode=mode)
        return DummyVecEnv([lambda: env.get_sb_env()])

    def train_model(self, algorithm="ppo"):
        """Train a specific model"""
        print(f"Training {algorithm.upper()} model...")

        # Create training environment
        train_env = self.create_env(self.train_data, mode="train")

        # Initialize model based on algorithm
        if algorithm.lower() == "ppo":
            model = PPO("MlpPolicy", train_env, verbose=1)
        elif algorithm.lower() == "a2c":
            model = A2C("MlpPolicy", train_env, verbose=1)
        elif algorithm.lower() == "sac":
            model = SAC("MlpPolicy", train_env, verbose=1)
        elif algorithm.lower() == "ddpg":
            model = DDPG("MlpPolicy", train_env, verbose=1)
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")

        # Train the model
        model.learn(
            total_timesteps=MODEL_CONFIG["total_timesteps"],
            log_interval=MODEL_CONFIG["log_interval"]
        )

        # Save the model
        model_path = MODELS_DIR / "trained" / f"{algorithm}_crypto_model"
        model.save(model_path)
        print(f"✓ Model saved to {model_path}")

        self.models[algorithm] = model
        return model

    def train_all_models(self):
        """Train all configured models"""
        for algorithm in MODEL_CONFIG["algorithms"]:
            try:
                self.train_model(algorithm)
            except Exception as e:
                print(f"❌ Error training {algorithm}: {e}")

    def backtest_model(self, algorithm="ppo"):
        """Backtest a trained model"""
        print(f"Backtesting {algorithm.upper()} model...")

        # Load model if not in memory
        if algorithm not in self.models:
            model_path = MODELS_DIR / "trained" / f"{algorithm}_crypto_model"
            if algorithm.lower() == "ppo":
                model = PPO.load(model_path)
            elif algorithm.lower() == "a2c":
                model = A2C.load(model_path)
            elif algorithm.lower() == "sac":
                model = SAC.load(model_path)
            elif algorithm.lower() == "ddpg":
                model = DDPG.load(model_path)
            self.models[algorithm] = model

        # Create trading environment
        trade_env = self.create_env(self.trade_data, mode="trade")

        # Run backtest
        obs = trade_env.reset()
        done = False
        actions = []

        while not done:
            action, _ = self.models[algorithm].predict(obs)
            obs, reward, done, info = trade_env.step(action)
            actions.append(action)

        # Get final results
        final_info = trade_env.get_attr('env_method', 'save_asset_memory')[0]()

        # Save results
        results_path = RESULTS_DIR / "backtests" / f"{algorithm}_backtest_results.csv"
        final_info.to_csv(results_path, index=False)
        print(f"✓ Backtest results saved to {results_path}")

        return final_info

if __name__ == "__main__":
    # Create directories
    os.makedirs(MODELS_DIR / "trained", exist_ok=True)
    os.makedirs(RESULTS_DIR / "backtests", exist_ok=True)

    # Initialize trader
    trader = CryptoTrader()
    trader.prepare_data()

    # Train models
    print("Starting model training...")
    trader.train_all_models()

    # Run backtests
    print("Starting backtesting...")
    for algorithm in MODEL_CONFIG["algorithms"]:
        try:
            trader.backtest_model(algorithm)
        except Exception as e:
            print(f"❌ Error backtesting {algorithm}: {e}")

    print("Training and backtesting completed!")
'''

    with open("src/models/train.py", "w") as f:
        f.write(training_script)
    print("✓ Created src/models/train.py")

def main():
    """Main setup function"""
    print("🚀 Setting up Crypto Trading Project with FinRL")
    print("=" * 50)

    # Create project structure
    create_project_structure()

    # Create configuration files
    create_requirements_file()
    create_config_files()

    # Create main scripts
    create_main_scripts()
    create_trading_environment()
    create_training_script()

    print("\n" + "=" * 50)
    print("✅ Project setup completed!")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Update .env file with your API keys")
    print("3. Run: python src/data/download_data.py")
    print("4. Run: python src/models/train.py")
    print("5. Start developing your trading strategies!")
    print("\nFor tutorials, check: https://github.com/AI4Finance-Foundation/FinRL-Tutorials")

if __name__ == "__main__":
    main()
