#!/usr/bin/env python3
"""
Installation Verification Script
Verifies that all components are properly installed and configured
"""

import sys
import os
import importlib
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported"""
    print("🔍 Testing package imports...")
    
    required_packages = {
        'finrl': 'FinRL framework',
        'stable_baselines3': 'Stable Baselines3 RL library',
        'gym': 'OpenAI Gym',
        'pandas': 'Data manipulation',
        'numpy': 'Numerical computing',
        'matplotlib': 'Plotting',
        'yfinance': 'Yahoo Finance data',
        'ccxt': 'Cryptocurrency exchange library',
        'ta': 'Technical analysis library'
    }
    
    failed_imports = []
    
    for package, description in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"  ✅ {package:<20} - {description}")
        except ImportError as e:
            print(f"  ❌ {package:<20} - {description} (Error: {e})")
            failed_imports.append(package)
    
    return len(failed_imports) == 0, failed_imports

def test_finrl_components():
    """Test specific FinRL components"""
    print("\n🧪 Testing FinRL components...")
    
    try:
        from finrl.meta.preprocessor.yahoodownloader import YahooDownloader
        print("  ✅ YahooDownloader")
    except ImportError as e:
        print(f"  ❌ YahooDownloader - {e}")
        return False
    
    try:
        from finrl.meta.preprocessor.preprocessors import FeatureEngineer
        print("  ✅ FeatureEngineer")
    except ImportError as e:
        print(f"  ❌ FeatureEngineer - {e}")
        return False
    
    try:
        from finrl.meta.env_cryptocurrency_trading.env_cryptocurrency import CryptocurrencyEnv
        print("  ✅ CryptocurrencyEnv")
    except ImportError as e:
        print(f"  ❌ CryptocurrencyEnv - {e}")
        return False
    
    return True

def test_stable_baselines3():
    """Test Stable Baselines3 algorithms"""
    print("\n🤖 Testing RL algorithms...")
    
    algorithms = ['PPO', 'A2C', 'SAC', 'DDPG']
    
    try:
        from stable_baselines3 import PPO, A2C, SAC, DDPG
        for algo in algorithms:
            print(f"  ✅ {algo}")
        return True
    except ImportError as e:
        print(f"  ❌ Stable Baselines3 algorithms - {e}")
        return False

def test_data_sources():
    """Test data source connections"""
    print("\n📊 Testing data sources...")
    
    # Test Yahoo Finance
    try:
        import yfinance as yf
        # Quick test download
        ticker = yf.Ticker("BTC-USD")
        info = ticker.info
        print("  ✅ Yahoo Finance connection")
    except Exception as e:
        print(f"  ❌ Yahoo Finance - {e}")
    
    # Test CCXT
    try:
        import ccxt
        exchange = ccxt.binance()
        print("  ✅ CCXT library")
    except Exception as e:
        print(f"  ❌ CCXT - {e}")

def test_project_structure():
    """Test project directory structure"""
    print("\n📁 Testing project structure...")
    
    required_dirs = [
        'config', 'data', 'models', 'results', 'src', 'notebooks'
    ]
    
    required_files = [
        'config/config.py',
        'src/data/download_data.py',
        'src/models/train.py',
        'src/trading/environment.py',
        'requirements.txt',
        '.env'
    ]
    
    # Check directories
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"  ✅ {dir_name}/")
        else:
            print(f"  ❌ {dir_name}/ - missing")
    
    # Check files
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"  ✅ {file_name}")
        else:
            print(f"  ❌ {file_name} - missing")

def test_configuration():
    """Test configuration files"""
    print("\n⚙️  Testing configuration...")
    
    try:
        sys.path.append('.')
        from config.config import CRYPTO_CONFIG, TRADING_CONFIG, MODEL_CONFIG
        
        print("  ✅ Configuration imported successfully")
        print(f"  📈 Cryptocurrencies: {len(CRYPTO_CONFIG['symbols'])}")
        print(f"  💰 Initial amount: ${TRADING_CONFIG['initial_amount']:,}")
        print(f"  🤖 Algorithms: {len(MODEL_CONFIG['algorithms'])}")
        
        return True
    except Exception as e:
        print(f"  ❌ Configuration error - {e}")
        return False

def run_quick_test():
    """Run a quick functionality test"""
    print("\n🚀 Running quick functionality test...")
    
    try:
        # Test data download (small sample)
        from finrl.meta.preprocessor.yahoodownloader import YahooDownloader
        
        print("  📊 Testing data download...")
        df = YahooDownloader(
            start_date="2024-01-01",
            end_date="2024-01-07",
            ticker_list=["BTC-USD"]
        ).fetch_data()
        
        if len(df) > 0:
            print(f"  ✅ Data download successful ({len(df)} rows)")
        else:
            print("  ❌ No data downloaded")
            return False
        
        # Test feature engineering
        from finrl.meta.preprocessor.preprocessors import FeatureEngineer
        
        print("  🔧 Testing feature engineering...")
        fe = FeatureEngineer(
            use_technical_indicator=True,
            tech_indicator_list=['macd', 'rsi_30'],
            use_vix=False,
            use_turbulence=False
        )
        
        processed_df = fe.preprocess_data(df)
        print(f"  ✅ Feature engineering successful ({processed_df.shape[1]} features)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Quick test failed - {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 FinRL Crypto Trading - Installation Verification")
    print("="*60)
    
    all_tests_passed = True
    
    # Test imports
    imports_ok, failed_imports = test_imports()
    if not imports_ok:
        all_tests_passed = False
        print(f"\n❌ Failed imports: {', '.join(failed_imports)}")
        print("💡 Install missing packages with: pip install -r requirements.txt")
    
    # Test FinRL components
    if imports_ok and not test_finrl_components():
        all_tests_passed = False
    
    # Test Stable Baselines3
    if imports_ok and not test_stable_baselines3():
        all_tests_passed = False
    
    # Test data sources
    if imports_ok:
        test_data_sources()
    
    # Test project structure
    test_project_structure()
    
    # Test configuration
    config_ok = test_configuration()
    if not config_ok:
        all_tests_passed = False
    
    # Run quick functionality test
    if imports_ok and config_ok:
        if not run_quick_test():
            all_tests_passed = False
    
    # Final result
    print("\n" + "="*60)
    if all_tests_passed:
        print("🎉 All tests passed! Your installation is ready.")
        print("\n📚 Next steps:")
        print("1. Update .env file with your API keys")
        print("2. Run: jupyter notebook notebooks/crypto_trading_starter.ipynb")
        print("3. Or run: python get_started.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        print("\n🔧 Common solutions:")
        print("1. pip install -r requirements.txt")
        print("2. python setup_crypto_trading.py")
        print("3. Check Python version (3.6+ required)")
    
    print("="*60)

if __name__ == "__main__":
    main()
