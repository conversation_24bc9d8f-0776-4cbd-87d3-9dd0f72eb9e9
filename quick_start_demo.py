#!/usr/bin/env python3
"""
Quick Start Demo for Crypto Trading with FinRL
This script demonstrates basic functionality without complex dependencies
"""

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv
import ta
from datetime import datetime, timedelta

def download_crypto_data(symbols=["BTC-USD", "ETH-USD"], period="1mo"):
    """Download cryptocurrency data"""
    print(f"📊 Downloading data for {symbols}...")
    
    all_data = []
    for symbol in symbols:
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            data['symbol'] = symbol
            data['date'] = data.index
            all_data.append(data)
            print(f"✅ Downloaded {len(data)} rows for {symbol}")
        except Exception as e:
            print(f"❌ Error downloading {symbol}: {e}")
    
    if all_data:
        combined_data = pd.concat(all_data, ignore_index=True)
        return combined_data
    else:
        return pd.DataFrame()

def add_technical_indicators(df):
    """Add basic technical indicators"""
    print("🔧 Adding technical indicators...")
    
    for symbol in df['symbol'].unique():
        symbol_data = df[df['symbol'] == symbol].copy()
        
        # Add technical indicators
        symbol_data['rsi'] = ta.momentum.RSIIndicator(symbol_data['Close']).rsi()
        symbol_data['macd'] = ta.trend.MACD(symbol_data['Close']).macd()
        symbol_data['bb_upper'] = ta.volatility.BollingerBands(symbol_data['Close']).bollinger_hband()
        symbol_data['bb_lower'] = ta.volatility.BollingerBands(symbol_data['Close']).bollinger_lband()
        symbol_data['sma_20'] = ta.trend.SMAIndicator(symbol_data['Close'], window=20).sma_indicator()
        
        # Update the main dataframe
        df.loc[df['symbol'] == symbol, 'rsi'] = symbol_data['rsi'].values
        df.loc[df['symbol'] == symbol, 'macd'] = symbol_data['macd'].values
        df.loc[df['symbol'] == symbol, 'bb_upper'] = symbol_data['bb_upper'].values
        df.loc[df['symbol'] == symbol, 'bb_lower'] = symbol_data['bb_lower'].values
        df.loc[df['symbol'] == symbol, 'sma_20'] = symbol_data['sma_20'].values
    
    # Drop NaN values
    df = df.dropna()
    print(f"✅ Added indicators, {len(df)} rows remaining after cleaning")
    return df

def visualize_data(df, symbol="BTC-USD"):
    """Create visualizations"""
    print(f"📈 Creating visualizations for {symbol}...")
    
    symbol_data = df[df['symbol'] == symbol].copy()
    
    if len(symbol_data) == 0:
        print(f"❌ No data found for {symbol}")
        return
    
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # Price and Bollinger Bands
    axes[0].plot(symbol_data['date'], symbol_data['Close'], label='Close Price', linewidth=2)
    axes[0].plot(symbol_data['date'], symbol_data['bb_upper'], label='BB Upper', alpha=0.7)
    axes[0].plot(symbol_data['date'], symbol_data['bb_lower'], label='BB Lower', alpha=0.7)
    axes[0].plot(symbol_data['date'], symbol_data['sma_20'], label='SMA 20', alpha=0.7)
    axes[0].set_title(f'{symbol} Price and Bollinger Bands')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # RSI
    axes[1].plot(symbol_data['date'], symbol_data['rsi'], label='RSI', color='orange')
    axes[1].axhline(y=70, color='r', linestyle='--', alpha=0.7, label='Overbought')
    axes[1].axhline(y=30, color='g', linestyle='--', alpha=0.7, label='Oversold')
    axes[1].set_title('RSI (Relative Strength Index)')
    axes[1].set_ylim(0, 100)
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # MACD
    axes[2].plot(symbol_data['date'], symbol_data['macd'], label='MACD', color='purple')
    axes[2].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    axes[2].set_title('MACD')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Summary statistics
    print(f"\n📊 Summary Statistics for {symbol}:")
    print(f"Price Range: ${symbol_data['Close'].min():.2f} - ${symbol_data['Close'].max():.2f}")
    print(f"Average RSI: {symbol_data['rsi'].mean():.2f}")
    print(f"Current RSI: {symbol_data['rsi'].iloc[-1]:.2f}")
    
    if symbol_data['rsi'].iloc[-1] > 70:
        print("🔴 RSI indicates OVERBOUGHT condition")
    elif symbol_data['rsi'].iloc[-1] < 30:
        print("🟢 RSI indicates OVERSOLD condition")
    else:
        print("🟡 RSI indicates NEUTRAL condition")

def simple_trading_strategy(df, symbol="BTC-USD"):
    """Implement a simple trading strategy"""
    print(f"🤖 Running simple trading strategy for {symbol}...")
    
    symbol_data = df[df['symbol'] == symbol].copy()
    
    if len(symbol_data) == 0:
        print(f"❌ No data found for {symbol}")
        return
    
    # Simple strategy: Buy when RSI < 30, Sell when RSI > 70
    symbol_data['signal'] = 0
    symbol_data.loc[symbol_data['rsi'] < 30, 'signal'] = 1  # Buy
    symbol_data.loc[symbol_data['rsi'] > 70, 'signal'] = -1  # Sell
    
    # Calculate returns
    symbol_data['returns'] = symbol_data['Close'].pct_change()
    symbol_data['strategy_returns'] = symbol_data['signal'].shift(1) * symbol_data['returns']
    
    # Calculate cumulative returns
    symbol_data['cumulative_returns'] = (1 + symbol_data['returns']).cumprod()
    symbol_data['cumulative_strategy_returns'] = (1 + symbol_data['strategy_returns']).cumprod()
    
    # Results
    total_return = (symbol_data['cumulative_returns'].iloc[-1] - 1) * 100
    strategy_return = (symbol_data['cumulative_strategy_returns'].iloc[-1] - 1) * 100
    
    print(f"📈 Buy & Hold Return: {total_return:.2f}%")
    print(f"🤖 Strategy Return: {strategy_return:.2f}%")
    
    if strategy_return > total_return:
        print("🎉 Strategy outperformed buy & hold!")
    else:
        print("📉 Strategy underperformed buy & hold")
    
    # Plot results
    plt.figure(figsize=(12, 6))
    plt.plot(symbol_data['date'], symbol_data['cumulative_returns'], label='Buy & Hold', linewidth=2)
    plt.plot(symbol_data['date'], symbol_data['cumulative_strategy_returns'], label='RSI Strategy', linewidth=2)
    plt.title(f'{symbol} - Strategy Performance Comparison')
    plt.xlabel('Date')
    plt.ylabel('Cumulative Returns')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

def main():
    """Main demo function"""
    print("🚀 Crypto Trading Demo with FinRL Components")
    print("=" * 50)
    
    # Download data
    crypto_data = download_crypto_data(["BTC-USD", "ETH-USD"], period="3mo")
    
    if crypto_data.empty:
        print("❌ No data downloaded. Exiting.")
        return
    
    # Add technical indicators
    crypto_data = add_technical_indicators(crypto_data)
    
    # Visualize BTC data
    visualize_data(crypto_data, "BTC-USD")
    
    # Run simple trading strategy
    simple_trading_strategy(crypto_data, "BTC-USD")
    
    print("\n" + "=" * 50)
    print("✅ Demo completed successfully!")
    print("\n🎯 Next Steps:")
    print("1. Explore the Jupyter notebook: notebooks/crypto_trading_starter.ipynb")
    print("2. Modify the trading strategy parameters")
    print("3. Try different cryptocurrencies")
    print("4. Implement more sophisticated RL algorithms")
    print("5. Add more technical indicators")
    
    print("\n📚 Learning Resources:")
    print("- FinRL Documentation: https://finrl.readthedocs.io/")
    print("- Stable Baselines3: https://stable-baselines3.readthedocs.io/")
    print("- Technical Analysis: https://technical-analysis-library-in-python.readthedocs.io/")

if __name__ == "__main__":
    main()
