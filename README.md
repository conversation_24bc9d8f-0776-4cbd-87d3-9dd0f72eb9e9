# Crypto Trading Project with FinRL

A comprehensive cryptocurrency trading project using FinRL (Financial Reinforcement Learning) framework for automated trading strategies.

## 🚀 Features

- **Multi-Algorithm Support**: PPO, A2C, SAC, DDPG reinforcement learning algorithms
- **Cryptocurrency Focus**: Optimized for crypto market trading (BTC, ETH, ADA, DOT, LINK)
- **Complete Pipeline**: Data download → Preprocessing → Training → Backtesting → Deployment
- **Technical Indicators**: MACD, Bollinger Bands, RSI, CCI, DX, SMA integration
- **Risk Management**: Built-in transaction costs, position limits, and risk controls
- **Paper Trading**: Safe testing environment before live trading

## 📁 Project Structure

```
crypto-trading-finrl/
├── config/                 # Configuration files
│   └── config.py           # Main configuration
├── data/                   # Data storage
│   ├── raw/               # Raw market data
│   ├── processed/         # Preprocessed data with indicators
│   └── indicators/        # Technical indicator data
├── models/                 # Trained models
│   ├── trained/           # Final trained models
│   └── checkpoints/       # Training checkpoints
├── results/               # Results and analysis
│   ├── backtests/         # Backtest results
│   └── performance/       # Performance metrics
├── src/                   # Source code
│   ├── data/              # Data processing scripts
│   ├── models/            # Model training scripts
│   ├── trading/           # Trading environment and strategies
│   └── utils/             # Utility functions
├── notebooks/             # Jupyter notebooks for analysis
├── logs/                  # Application logs
└── tests/                 # Unit tests
```

## 🛠️ Installation

### Prerequisites
- Python 3.6 or higher
- pip package manager

### Quick Setup

1. **Clone and setup the project:**
```bash
git clone <your-repo-url>
cd crypto-trading-finrl
python setup_crypto_trading.py
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment:**
   - Edit `.env` file with your API keys
   - Modify `config/config.py` for your trading preferences

### Manual Installation

If you prefer manual setup:

```bash
# Install FinRL and dependencies
pip install finrl
pip install stable-baselines3
pip install ccxt yfinance ta

# Install additional packages
pip install jupyter plotly dash python-dotenv
```

## 🔧 Configuration

### API Keys Setup

Edit the `.env` file with your API credentials:

```env
# Binance API (for live data)
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

# Alpaca API (for paper trading)
ALPACA_API_KEY=your_alpaca_api_key
ALPACA_SECRET_KEY=your_alpaca_secret_key
```

### Trading Configuration

Modify `config/config.py` to customize:

- **Initial capital**: Starting amount for trading
- **Transaction costs**: Broker fees and slippage
- **Cryptocurrencies**: Which coins to trade
- **Technical indicators**: Which indicators to use
- **Risk parameters**: Position limits and risk controls

## 🚀 Quick Start

### 1. Download Data
```bash
python src/data/download_data.py
```

### 2. Train Models
```bash
python src/models/train.py
```

### 3. Run Backtests
The training script automatically runs backtests. Results are saved in `results/backtests/`

### 4. Analyze Results
Use the provided Jupyter notebooks in the `notebooks/` directory for analysis.

## 📊 Supported Cryptocurrencies

Default configuration includes:
- **BTC-USD** (Bitcoin)
- **ETH-USD** (Ethereum)
- **ADA-USD** (Cardano)
- **DOT-USD** (Polkadot)
- **LINK-USD** (Chainlink)

Easily add more by modifying the `CRYPTO_CONFIG` in `config/config.py`.

## 🧠 Reinforcement Learning Algorithms

### Supported Algorithms:
1. **PPO** (Proximal Policy Optimization) - Recommended for beginners
2. **A2C** (Advantage Actor-Critic) - Fast and stable
3. **SAC** (Soft Actor-Critic) - Good for continuous action spaces
4. **DDPG** (Deep Deterministic Policy Gradient) - For deterministic policies

### Algorithm Selection Guide:
- **Start with PPO**: Most stable and widely used
- **Use A2C**: For faster training with good performance
- **Try SAC**: For more sophisticated trading strategies
- **Use DDPG**: For precise position sizing

## 📈 Technical Indicators

Built-in technical indicators:
- **MACD**: Moving Average Convergence Divergence
- **Bollinger Bands**: Upper and lower bands
- **RSI**: Relative Strength Index (30-period)
- **CCI**: Commodity Channel Index (30-period)
- **DX**: Directional Movement Index (30-period)
- **SMA**: Simple Moving Averages (30 and 60-period)

## 🔍 Backtesting

The framework provides comprehensive backtesting with:
- **Historical performance**: Returns, Sharpe ratio, max drawdown
- **Trade analysis**: Win rate, average trade duration
- **Risk metrics**: Volatility, beta, alpha
- **Comparison**: Against buy-and-hold strategy

## ⚠️ Risk Management

Built-in risk controls:
- **Position limits**: Maximum position size per asset
- **Transaction costs**: Realistic trading fees
- **Slippage modeling**: Market impact simulation
- **Drawdown limits**: Automatic position reduction

## 🔗 Useful Resources

- **FinRL Documentation**: https://finrl.readthedocs.io/
- **FinRL GitHub**: https://github.com/AI4Finance-Foundation/FinRL
- **FinRL Tutorials**: https://github.com/AI4Finance-Foundation/FinRL-Tutorials
- **Stable Baselines3**: https://stable-baselines3.readthedocs.io/

## 📝 Next Steps

1. **Experiment with parameters**: Modify trading and model configurations
2. **Add new indicators**: Implement custom technical indicators
3. **Extend to more assets**: Add more cryptocurrencies or other assets
4. **Implement live trading**: Connect to real exchanges (use paper trading first!)
5. **Optimize strategies**: Use hyperparameter tuning for better performance

## ⚠️ Disclaimer

This project is for educational and research purposes only. Cryptocurrency trading involves significant risk and can result in financial loss. Always:

- Start with paper trading
- Never invest more than you can afford to lose
- Thoroughly backtest strategies before live trading
- Consider consulting with financial advisors

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
