#!/usr/bin/env python3
"""
Simple Crypto Trading Demo - Non-interactive version
"""

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import ta
from datetime import datetime, timedelta

def download_crypto_data(symbols=["BTC-USD", "ETH-USD"], period="1mo"):
    """Download cryptocurrency data"""
    print(f"📊 Downloading data for {symbols}...")
    
    all_data = []
    for symbol in symbols:
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            data['symbol'] = symbol
            data['date'] = data.index
            all_data.append(data)
            print(f"✅ Downloaded {len(data)} rows for {symbol}")
        except Exception as e:
            print(f"❌ Error downloading {symbol}: {e}")
    
    if all_data:
        combined_data = pd.concat(all_data, ignore_index=True)
        return combined_data
    else:
        return pd.DataFrame()

def add_technical_indicators(df):
    """Add basic technical indicators"""
    print("🔧 Adding technical indicators...")
    
    for symbol in df['symbol'].unique():
        symbol_data = df[df['symbol'] == symbol].copy()
        
        # Add technical indicators
        symbol_data['rsi'] = ta.momentum.RSIIndicator(symbol_data['Close']).rsi()
        symbol_data['macd'] = ta.trend.MACD(symbol_data['Close']).macd()
        symbol_data['bb_upper'] = ta.volatility.BollingerBands(symbol_data['Close']).bollinger_hband()
        symbol_data['bb_lower'] = ta.volatility.BollingerBands(symbol_data['Close']).bollinger_lband()
        symbol_data['sma_20'] = ta.trend.SMAIndicator(symbol_data['Close'], window=20).sma_indicator()
        
        # Update the main dataframe
        df.loc[df['symbol'] == symbol, 'rsi'] = symbol_data['rsi'].values
        df.loc[df['symbol'] == symbol, 'macd'] = symbol_data['macd'].values
        df.loc[df['symbol'] == symbol, 'bb_upper'] = symbol_data['bb_upper'].values
        df.loc[df['symbol'] == symbol, 'bb_lower'] = symbol_data['bb_lower'].values
        df.loc[df['symbol'] == symbol, 'sma_20'] = symbol_data['sma_20'].values
    
    # Drop NaN values
    df = df.dropna()
    print(f"✅ Added indicators, {len(df)} rows remaining after cleaning")
    return df

def analyze_data(df, symbol="BTC-USD"):
    """Analyze cryptocurrency data"""
    print(f"📈 Analyzing {symbol}...")
    
    symbol_data = df[df['symbol'] == symbol].copy()
    
    if len(symbol_data) == 0:
        print(f"❌ No data found for {symbol}")
        return
    
    # Summary statistics
    print(f"\n📊 Summary Statistics for {symbol}:")
    print(f"Price Range: ${symbol_data['Close'].min():.2f} - ${symbol_data['Close'].max():.2f}")
    print(f"Average Price: ${symbol_data['Close'].mean():.2f}")
    print(f"Current Price: ${symbol_data['Close'].iloc[-1]:.2f}")
    print(f"Average RSI: {symbol_data['rsi'].mean():.2f}")
    print(f"Current RSI: {symbol_data['rsi'].iloc[-1]:.2f}")
    
    # RSI Analysis
    if symbol_data['rsi'].iloc[-1] > 70:
        print("🔴 RSI indicates OVERBOUGHT condition")
    elif symbol_data['rsi'].iloc[-1] < 30:
        print("🟢 RSI indicates OVERSOLD condition")
    else:
        print("🟡 RSI indicates NEUTRAL condition")
    
    # Price trend
    recent_change = ((symbol_data['Close'].iloc[-1] / symbol_data['Close'].iloc[-7]) - 1) * 100
    print(f"📈 7-day price change: {recent_change:.2f}%")
    
    return symbol_data

def simple_trading_strategy(df, symbol="BTC-USD"):
    """Implement a simple trading strategy"""
    print(f"\n🤖 Running simple RSI trading strategy for {symbol}...")
    
    symbol_data = df[df['symbol'] == symbol].copy()
    
    if len(symbol_data) == 0:
        print(f"❌ No data found for {symbol}")
        return
    
    # Simple strategy: Buy when RSI < 30, Sell when RSI > 70
    symbol_data['signal'] = 0
    symbol_data.loc[symbol_data['rsi'] < 30, 'signal'] = 1  # Buy
    symbol_data.loc[symbol_data['rsi'] > 70, 'signal'] = -1  # Sell
    
    # Calculate returns
    symbol_data['returns'] = symbol_data['Close'].pct_change()
    symbol_data['strategy_returns'] = symbol_data['signal'].shift(1) * symbol_data['returns']
    
    # Calculate cumulative returns
    symbol_data['cumulative_returns'] = (1 + symbol_data['returns']).cumprod()
    symbol_data['cumulative_strategy_returns'] = (1 + symbol_data['strategy_returns']).cumprod()
    
    # Results
    total_return = (symbol_data['cumulative_returns'].iloc[-1] - 1) * 100
    strategy_return = (symbol_data['cumulative_strategy_returns'].iloc[-1] - 1) * 100
    
    print(f"📈 Buy & Hold Return: {total_return:.2f}%")
    print(f"🤖 RSI Strategy Return: {strategy_return:.2f}%")
    
    if strategy_return > total_return:
        print("🎉 Strategy outperformed buy & hold!")
    else:
        print("📉 Strategy underperformed buy & hold")
    
    # Count trades
    trades = symbol_data['signal'].abs().sum()
    print(f"🔄 Total trades executed: {trades}")
    
    return symbol_data

def generate_trading_signals(df, symbol="BTC-USD"):
    """Generate current trading signals"""
    print(f"\n🎯 Current Trading Signals for {symbol}:")
    
    symbol_data = df[df['symbol'] == symbol].copy()
    
    if len(symbol_data) == 0:
        print(f"❌ No data found for {symbol}")
        return
    
    latest = symbol_data.iloc[-1]
    
    print(f"Current Price: ${latest['Close']:.2f}")
    print(f"RSI: {latest['rsi']:.2f}")
    print(f"MACD: {latest['macd']:.4f}")
    
    # Generate signals
    signals = []
    
    if latest['rsi'] < 30:
        signals.append("🟢 BUY - RSI oversold")
    elif latest['rsi'] > 70:
        signals.append("🔴 SELL - RSI overbought")
    
    if latest['Close'] > latest['sma_20']:
        signals.append("📈 BULLISH - Price above SMA20")
    else:
        signals.append("📉 BEARISH - Price below SMA20")
    
    if latest['macd'] > 0:
        signals.append("🚀 POSITIVE - MACD above zero")
    else:
        signals.append("⬇️ NEGATIVE - MACD below zero")
    
    if signals:
        for signal in signals:
            print(signal)
    else:
        print("🟡 NEUTRAL - No strong signals")

def main():
    """Main demo function"""
    print("🚀 Crypto Trading Demo with FinRL Components")
    print("=" * 60)
    
    # Download data
    crypto_data = download_crypto_data(["BTC-USD", "ETH-USD"], period="3mo")
    
    if crypto_data.empty:
        print("❌ No data downloaded. Exiting.")
        return
    
    # Add technical indicators
    crypto_data = add_technical_indicators(crypto_data)
    
    # Analyze both cryptocurrencies
    for symbol in ["BTC-USD", "ETH-USD"]:
        print("\n" + "=" * 40)
        analyze_data(crypto_data, symbol)
        simple_trading_strategy(crypto_data, symbol)
        generate_trading_signals(crypto_data, symbol)
    
    print("\n" + "=" * 60)
    print("✅ Demo completed successfully!")
    print("\n🎯 What we accomplished:")
    print("✅ Downloaded real cryptocurrency data")
    print("✅ Added technical indicators (RSI, MACD, Bollinger Bands)")
    print("✅ Implemented a simple trading strategy")
    print("✅ Analyzed performance vs buy-and-hold")
    print("✅ Generated current trading signals")
    
    print("\n🚀 Next Steps:")
    print("1. Open Jupyter notebook: jupyter notebook notebooks/crypto_trading_starter.ipynb")
    print("2. Experiment with different strategies")
    print("3. Add more cryptocurrencies")
    print("4. Implement reinforcement learning models")
    print("5. Set up paper trading")
    
    print("\n📚 Learning Resources:")
    print("- FinRL Documentation: https://finrl.readthedocs.io/")
    print("- Stable Baselines3: https://stable-baselines3.readthedocs.io/")
    print("- Technical Analysis: https://technical-analysis-library-in-python.readthedocs.io/")
    
    print("\n⚠️ Important Reminder:")
    print("This is for educational purposes only. Never invest more than you can afford to lose!")

if __name__ == "__main__":
    main()
