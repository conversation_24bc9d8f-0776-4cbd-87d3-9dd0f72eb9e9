
import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
RESULTS_DIR = PROJECT_ROOT / "results"
LOGS_DIR = PROJECT_ROOT / "logs"

# Trading configuration
TRADING_CONFIG = {
    "initial_amount": 100000,  # Starting capital in USD
    "transaction_cost_pct": 0.001,  # 0.1% transaction cost
    "max_stock": 1,  # Maximum position size
    "hmax": 100,  # Maximum shares to trade
    "print_verbosity": 2,
    "day_trading": True,
    "cache_indicator_data": True,
}

# Cryptocurrency configuration
CRYPTO_CONFIG = {
    "symbols": ["BTC-USD", "ETH-USD", "ADA-USD", "DOT-USD", "LINK-USD"],
    "start_date": "2020-01-01",
    "end_date": "2024-01-01",
    "time_interval": "1d",  # 1 day intervals
    "technical_indicators": [
        "macd", "boll_ub", "boll_lb", "rsi_30", "cci_30", 
        "dx_30", "close_30_sma", "close_60_sma"
    ]
}

# Model configuration
MODEL_CONFIG = {
    "algorithms": ["ppo", "a2c", "ddpg", "sac"],
    "total_timesteps": 100000,
    "eval_freq": 5000,
    "n_eval_episodes": 5,
    "log_interval": 100,
}

# Environment variables
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY", "")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY", "")
ALPACA_API_KEY = os.getenv("ALPACA_API_KEY", "")
ALPACA_SECRET_KEY = os.getenv("ALPACA_SECRET_KEY", "")
