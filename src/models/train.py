
"""
Model Training Script for Crypto Trading
"""

import os
import pandas as pd
from stable_baselines3 import PPO, A2C, SAC, DDPG
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import EvalCallback
from finrl.meta.preprocessor.preprocessors import data_split
from src.trading.environment import CryptoTradingEnv, prepare_training_data
from config.config import MODEL_CONFIG, MODELS_DIR, RESULTS_DIR
import warnings
warnings.filterwarnings("ignore")

class CryptoTrader:
    def __init__(self):
        self.models = {}
        self.train_data = None
        self.trade_data = None

    def prepare_data(self):
        """Prepare training and testing data"""
        self.train_data, self.trade_data = prepare_training_data()
        print(f"Training data shape: {self.train_data.shape}")
        print(f"Trading data shape: {self.trade_data.shape}")

    def create_env(self, data, mode="train"):
        """Create trading environment"""
        env = CryptoTradingEnv(data, mode=mode)
        return DummyVecEnv([lambda: env.get_sb_env()])

    def train_model(self, algorithm="ppo"):
        """Train a specific model"""
        print(f"Training {algorithm.upper()} model...")

        # Create training environment
        train_env = self.create_env(self.train_data, mode="train")

        # Initialize model based on algorithm
        if algorithm.lower() == "ppo":
            model = PPO("MlpPolicy", train_env, verbose=1)
        elif algorithm.lower() == "a2c":
            model = A2C("MlpPolicy", train_env, verbose=1)
        elif algorithm.lower() == "sac":
            model = SAC("MlpPolicy", train_env, verbose=1)
        elif algorithm.lower() == "ddpg":
            model = DDPG("MlpPolicy", train_env, verbose=1)
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")

        # Train the model
        model.learn(
            total_timesteps=MODEL_CONFIG["total_timesteps"],
            log_interval=MODEL_CONFIG["log_interval"]
        )

        # Save the model
        model_path = MODELS_DIR / "trained" / f"{algorithm}_crypto_model"
        model.save(model_path)
        print(f"✓ Model saved to {model_path}")

        self.models[algorithm] = model
        return model

    def train_all_models(self):
        """Train all configured models"""
        for algorithm in MODEL_CONFIG["algorithms"]:
            try:
                self.train_model(algorithm)
            except Exception as e:
                print(f"❌ Error training {algorithm}: {e}")

    def backtest_model(self, algorithm="ppo"):
        """Backtest a trained model"""
        print(f"Backtesting {algorithm.upper()} model...")

        # Load model if not in memory
        if algorithm not in self.models:
            model_path = MODELS_DIR / "trained" / f"{algorithm}_crypto_model"
            if algorithm.lower() == "ppo":
                model = PPO.load(model_path)
            elif algorithm.lower() == "a2c":
                model = A2C.load(model_path)
            elif algorithm.lower() == "sac":
                model = SAC.load(model_path)
            elif algorithm.lower() == "ddpg":
                model = DDPG.load(model_path)
            self.models[algorithm] = model

        # Create trading environment
        trade_env = self.create_env(self.trade_data, mode="trade")

        # Run backtest
        obs = trade_env.reset()
        done = False
        actions = []

        while not done:
            action, _ = self.models[algorithm].predict(obs)
            obs, reward, done, info = trade_env.step(action)
            actions.append(action)

        # Get final results
        final_info = trade_env.get_attr('env_method', 'save_asset_memory')[0]()

        # Save results
        results_path = RESULTS_DIR / "backtests" / f"{algorithm}_backtest_results.csv"
        final_info.to_csv(results_path, index=False)
        print(f"✓ Backtest results saved to {results_path}")

        return final_info

if __name__ == "__main__":
    # Create directories
    os.makedirs(MODELS_DIR / "trained", exist_ok=True)
    os.makedirs(RESULTS_DIR / "backtests", exist_ok=True)

    # Initialize trader
    trader = CryptoTrader()
    trader.prepare_data()

    # Train models
    print("Starting model training...")
    trader.train_all_models()

    # Run backtests
    print("Starting backtesting...")
    for algorithm in MODEL_CONFIG["algorithms"]:
        try:
            trader.backtest_model(algorithm)
        except Exception as e:
            print(f"❌ Error backtesting {algorithm}: {e}")

    print("Training and backtesting completed!")
