
"""
Cryptocurrency Trading Environment using FinRL
"""

import numpy as np
import pandas as pd
from finrl.meta.env_cryptocurrency_trading.env_cryptocurrency import CryptocurrencyEnv
from finrl.meta.preprocessor.preprocessors import data_split
from config.config import TRADING_CONFIG, CRYPTO_CONFIG, DATA_DIR
import gym

class CryptoTradingEnv:
    def __init__(self, df, mode="train"):
        self.df = df
        self.mode = mode
        self.env = None

    def create_env(self):
        """Create the trading environment"""
        env = CryptocurrencyEnv(
            df=self.df,
            initial_amount=TRADING_CONFIG["initial_amount"],
            transaction_cost_pct=TRADING_CONFIG["transaction_cost_pct"],
            mode=self.mode,
            day_trading=TRADING_CONFIG["day_trading"]
        )
        return env

    def get_sb_env(self):
        """Get environment compatible with stable-baselines3"""
        env = self.create_env()
        return env

def prepare_training_data():
    """Prepare data for training"""
    # Load processed data
    data_path = DATA_DIR / "processed" / "crypto_processed_data.csv"
    df = pd.read_csv(data_path)

    # Split data
    train_data = data_split(df, start="2020-01-01", end="2022-01-01")
    trade_data = data_split(df, start="2022-01-01", end="2024-01-01")

    return train_data, trade_data

if __name__ == "__main__":
    train_data, trade_data = prepare_training_data()

    # Create training environment
    train_env = CryptoTradingEnv(train_data, mode="train")
    env = train_env.get_sb_env()

    print(f"Environment created successfully!")
    print(f"Action space: {env.action_space}")
    print(f"Observation space: {env.observation_space}")
