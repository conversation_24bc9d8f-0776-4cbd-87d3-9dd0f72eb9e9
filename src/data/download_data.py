
"""
Cryptocurrency Data Downloader
Downloads and preprocesses cryptocurrency data for FinRL
"""

import pandas as pd
import yfinance as yf
from finrl.meta.preprocessor.yahoodownloader import YahooDownloader
from finrl.meta.preprocessor.preprocessors import FeatureEngineer, data_split
from config.config import CRYPTO_CONFIG, DATA_DIR
import os

def download_crypto_data():
    """Download cryptocurrency data"""
    print("Downloading cryptocurrency data...")
    
    # Download data using FinRL's YahooDownloader
    df = YahooDownloader(
        start_date=CRYPTO_CONFIG["start_date"],
        end_date=CRYPTO_CONFIG["end_date"],
        ticker_list=CRYPTO_CONFIG["symbols"]
    ).fetch_data()
    
    # Save raw data
    raw_data_path = DATA_DIR / "raw" / "crypto_raw_data.csv"
    df.to_csv(raw_data_path, index=False)
    print(f"✓ Raw data saved to {raw_data_path}")
    
    return df

def preprocess_data(df):
    """Add technical indicators and preprocess data"""
    print("Adding technical indicators...")
    
    # Feature engineering
    fe = FeatureEngineer(
        use_technical_indicator=True,
        tech_indicator_list=CRYPTO_CONFIG["technical_indicators"],
        use_vix=False,
        use_turbulence=True,
        user_defined_feature=False
    )
    
    processed_df = fe.preprocess_data(df)
    
    # Save processed data
    processed_data_path = DATA_DIR / "processed" / "crypto_processed_data.csv"
    processed_df.to_csv(processed_data_path, index=False)
    print(f"✓ Processed data saved to {processed_data_path}")
    
    return processed_df

if __name__ == "__main__":
    # Create data directories
    os.makedirs(DATA_DIR / "raw", exist_ok=True)
    os.makedirs(DATA_DIR / "processed", exist_ok=True)
    
    # Download and process data
    raw_data = download_crypto_data()
    processed_data = preprocess_data(raw_data)
    
    print("Data download and preprocessing completed!")
    print(f"Data shape: {processed_data.shape}")
    print(f"Date range: {processed_data['date'].min()} to {processed_data['date'].max()}")
